import Plan1Content from '@/modules/plan/plan-1'
import { Metadata } from 'next';
import React from 'react'

export const metadata: Metadata = {
  title: "Plan 1 Student Loans UK - Complete Guide to Pre-2012 Loans",
  description: "Everything you need to know about Plan 1 student loans in the UK including repayment thresholds, interest rates, and total cost calculations.",
  applicationName: "Student Loan Calculator UK",
  authors: [{ name: "Student Loan Calculator UK Team" }],
  generator: "Next.js",
  keywords: [
    "student loan calculator",
    "uk student loan",
    "plan 2 calculator",
    "plan 5 calculator",
    "student finance",
    "loan repayments",
  ],
  creator: "Student Loan Calculator UK",
  publisher: "Student Loan Calculator UK",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://studentloancalculator.uk/plans/plan-1/",
  },
  formatDetection: {
    telephone: false,
    address: false,
    email: false,
  },
  openGraph: {
    title: "Plan 1 Student Loans UK Guide (Pre-2012 Students)",
    description: "Comprehensive explanation of Plan 1 student loans including thresholds, interest rates, repayment calculations and comparisons with other plans.",
    url: "https://studentloancalculator.uk/plans/plan-1/",
    siteName: "Student Loan Calculator UK",
    locale: "en_GB",
    images: [
      {
        url: "https://studentloancalculator.uk/images/og/plans/plan-1.jpg",
        width: 1200,
        height: 630,
        alt: "Plan 1 Student Loans Guide",
      },
    ],
    type: "article",
  },
  twitter: {
    card: "summary_large_image",
    creator: "@UKStudentCalc",
    title: "Plan 1 Student Loans Explained (Pre-2012)",
    description: "Everything you need to know about Plan 1 loans - thresholds, interest & calculations",
    images: ["https://studentloancalculator.uk/images/og/plans/plan-1.jpg"],
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
    other: [{ rel: "mask-icon", url: "/safari-pinned-tab.svg" }],
  },
};


const Plan1 = () => {
  return (
    <div>
        <Plan1Content/>
    </div>
  )
}

export default Plan1