import Link from "next/link";

export default function SiteFooter() {
  return (
    <footer className="bg-gray-50 pt-16 pb-8 mt-10" aria-label="Footer navigation">
      <div className="max-w-6xl mx-auto px-6">
        {/* Top grid */}
        <div className="grid grid-cols-2 md:grid-cols-12 gap-8 pb-10 border-b border-gray-200">
          {/* Brand + blurb + socials */}
          <div className="col-span-2 md:col-span-5">
            <div className="flex items-center mb-6">
              <div className="w-10 h-10 bg-[#1A4FCC] rounded-lg flex items-center justify-center mr-3" aria-hidden="true">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <span className="text-xl font-bold text-[#1A4FCC] font-['Poppins']">
                Student Loan Calculator UK
              </span>
            </div>

            <p className="text-gray-600 mb-6 font-['Open_Sans']">
              Helping UK students and graduates understand and manage their student loans with accurate, up-to-date tools and resources.
            </p>

            <div className="flex space-x-4">
              <a
                href="#"
                aria-label="Twitter"
                className="w-10 h-10 rounded-full bg-[#1A4FCC] flex items-center justify-center text-white hover:bg-blue-700 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
                </svg>
              </a>

              <a
                href="#"
                aria-label="LinkedIn"
                className="w-10 h-10 rounded-full bg-[#1A4FCC] flex items-center justify-center text-white hover:bg-blue-700 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                </svg>
              </a>

              <a
                href="#"
                aria-label="Facebook"
                className="w-10 h-10 rounded-full bg-[#1A4FCC] flex items-center justify-center text-white hover:bg-blue-700 transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z" />
                </svg>
              </a>
            </div>
          </div>

          {/* Tools */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="font-semibold text-gray-800 mb-4 text-lg font-['Inter']">Tools</h3>
            <ul className="space-y-3 font-['Open_Sans']">
              <li>
                <Link href="/calculators" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Calculators
                </Link>
              </li>
              <li>
                <Link href="/compare" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Compare Plans
                </Link>
              </li>
              <li>
                <Link href="/plans" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Loan Plans
                </Link>
              </li>
              <li>
                <Link href="/repayment" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Repayment Help
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="font-semibold text-gray-800 mb-4 text-lg font-['Inter']">Resources</h3>
            <ul className="space-y-3 font-['Open_Sans']">
              <li>
                <Link href="/guides" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Guides
                </Link>
              </li>
              <li>
                <Link href="/faqs" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  FAQs
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div className="col-span-2 md:col-span-3">
            <h3 className="font-semibold text-gray-800 mb-4 text-lg font-['Inter']">Legal</h3>
            <ul className="space-y-3 font-['Open_Sans']">
              <li>
                <Link href="/privacy-policy" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Terms of Use
                </Link>
              </li>
              <li>
                <Link href="/cookie-policy" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Cookie Policy
                </Link>
              </li>
              <li>
                <Link href="/disclaimer" className="text-gray-600 hover:text-[#1A4FCC] transition-colors">
                  Financial Disclaimer
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom bar */}
        <div className="pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-500 font-['Open_Sans'] text-center md:text-left mb-4 md:mb-0">
            © 2025 Student Loan Calculator UK. All rights reserved.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
            <a href="#" className="hover:text-[#1A4FCC] transition-colors">
              Accessibility
            </a>
            <span className="hidden md:inline">•</span>
            <a href="#" className="hover:text-[#1A4FCC] transition-colors">
              Sitemap
            </a>
            <span className="hidden md:inline">•</span>
            <a href="#" className="hover:text-[#1A4FCC] transition-colors">
              Data Protection
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
