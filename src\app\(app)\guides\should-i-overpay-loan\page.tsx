import OverpayStudentLoanGuide from '@/modules/guides/should-i-overpay-loan'
import { Metadata } from 'next';
import React from 'react'

export const metadata: Metadata = {
  title: "Should I Overpay My Student Loan? UK Guide",
  description: "Find out if you should make voluntary overpayments on your UK student loan. Pros, cons, and when it makes sense to pay extra.",
  applicationName: "Student Loan Calculator UK",
  authors: [{ name: "Student Loan Calculator UK Team" }],
  generator: "Next.js",
  keywords: [
    "student loan calculator",
    "uk student loan",
    "plan 2 calculator",
    "plan 5 calculator",
    "student finance",
    "loan repayments",
  ],
  creator: "Student Loan Calculator UK",
  publisher: "Student Loan Calculator UK",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://studentloancalculator.uk/guides/should-i-overpay-loan/",
  },
  formatDetection: {
    telephone: false,
    address: false,
    email: false,
  },
  openGraph: {
    title: "Should I Overpay My Student Loan? UK Guide",
    description: "Find out if you should make voluntary overpayments on your UK student loan. Pros, cons, and when it makes sense to pay extra.",
    url: "https://studentloancalculator.uk/guides/should-i-overpay-loan/",
    siteName: "Student Loan Calculator UK",
    locale: "en_GB",
    type: "article",
  },
  twitter: {
    card: "summary_large_image",
    title: "Student Loan Calculator UK",
    description: "Calculate your UK student loan repayments with our free calculators.",
    images: ["http://localhost:3000/og-image.jpg"],
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [{ url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" }],
    other: [{ rel: "mask-icon", url: "/safari-pinned-tab.svg" }],
  },
};

const OverpayLoan = () => {
  return (
    <div>
        <OverpayStudentLoanGuide/>
    </div>
  )
}

export default OverpayLoan