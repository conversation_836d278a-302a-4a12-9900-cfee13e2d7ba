import Link from "next/link";

export default function FAQPreview() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": [
              {
                "@type": "Question",
                "name": "How are student loan repayments calculated?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Repayments are calculated as a percentage of your income above the repayment threshold. For most plans, this is 9% of your income above the threshold."
                }
              },
              {
                "@type": "Question",
                "name": "Should I overpay my student loan?",
                "acceptedAnswer": {
                  "@type": "Answer",
                  "text": "Whether to overpay depends on your income, loan plan, and financial goals."
                }
              }
            ]
          })
        }}
      />

      <section className="max-w-6xl mx-auto px-6" aria-labelledby="faq-heading">
        <div className="text-center mb-12">
          <h2 id="faq-heading" className="text-3xl font-bold text-gray-800 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Get quick answers to common questions about UK student loans
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-xl shadow-sm">
            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              How are student loan repayments calculated?
            </h3>
            <p className="text-gray-600 mb-4">
              Repayments are calculated as a percentage of your income above the repayment threshold. For
              most plans, this is 9% of your income above the threshold.
            </p>
            <Link
              href="/guides/how-student-loans-work-uk/"
              aria-label="Learn more about how student loan repayments are calculated"
              className="text-blue-600 font-medium hover:text-blue-800 inline-flex items-center"
            >
              Learn More
              <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                />
              </svg>
            </Link>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-sm">
            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              Should I overpay my student loan?
            </h3>
            <p className="text-gray-600 mb-4">
              Whether to overpay depends on your income, loan plan, and financial goals. Our guide helps
              you make an informed decision.
            </p>
            <Link
              href="/guides/should-i-overpay-loan/"
              aria-label="Learn more about student loan overpayments"
              className="text-blue-600 font-medium hover:text-blue-800 inline-flex items-center"
            >
              Learn More
              <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                />
              </svg>
            </Link>
          </div>
        </div>

        <div className="text-center mt-8">
          <Link
            href="/faq/"
            aria-label="View all frequently asked questions about student loans"
            className="inline-flex items-center text-blue-700 hover:text-blue-800 font-medium"
          >
            View All FAQs
            <svg className="w-5 h-5 ml-1" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
              />
            </svg>
          </Link>
        </div>
      </section>
    </>
  );
}
