import Plan4Content from '@/modules/plan/plan-4'
import { Metadata } from 'next';
import React from 'react'

export const metadata: Metadata = {
  title: "Plan 4 Student Loans - Complete Guide | Student Loan Calculator UK",
  description: "Everything you need to know about Plan 4 student loans for Scottish students, including repayment thresholds and interest rates.",
  applicationName: "Student Loan Calculator UK",
  authors: [{ name: "Student Loan Calculator UK Team" }],
  generator: "Next.js",
  keywords: [
    "student loan calculator",
    "uk student loan",
    "plan 2 calculator",
    "plan 5 calculator",
    "student finance",
    "loan repayments",
  ],
  creator: "Student Loan Calculator UK",
  publisher: "Student Loan Calculator UK",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://studentloancalculator.uk/plans/plan-4/",
  },
  formatDetection: {
    telephone: false,
    address: false,
    email: false,
  },
  openGraph: {
    title: "Plan 4 Student Loans - Complete Guide",
    description: "Everything you need to know about Plan 4 student loans for Scottish students, including repayment thresholds and interest rates.",
    url: "https://studentloancalculator.uk/plans/plan-4/",
    siteName: "Student Loan Calculator UK",
    locale: "en_GB",
    images: [
      {
        url: "https://studentloancalculator.uk/images/og/plans/plan-4.jpg",
        width: 1200,
        height: 630,
        alt: "Plan 4 Student Loans",
      },
    ],
    type: "article",
  },
  twitter: {
    card: "summary_large_image",
    creator: "@UKStudentCalc",
    title: "Plan 4 Student Loans - Complete Guide",
    description: "Everything you need to know about Plan 4 student loans for Scottish students, including repayment thresholds and interest rates.",
    images: ["https://studentloancalculator.uk/images/og/plans/plan-4.jpg"],
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
    other: [{ rel: "mask-icon", url: "/safari-pinned-tab.svg" }],
  },
};

const Plan4Page = () => {
  return (
    <div>
        <Plan4Content/>
    </div>
  )
}

export default Plan4Page