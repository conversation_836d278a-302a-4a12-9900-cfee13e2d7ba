// app/guides/student-loans-high-earners/page.tsx
import Link from "next/link";

export default function Page() {
  return <HighEarnersGuide />;
}

export function HighEarnersGuide() {
  return (
    <main className="space-y-12 pb-16">
      {/* Hero / breadcrumb */}
      <section className="bg-blue-50 rounded-2xl p-8 md:p-12">
        <div className="max-w-4xl mx-auto">
          <nav aria-label="breadcrumb">
            <ol className="flex items-center space-x-1 text-sm text-gray-600">
              <li>
                <Link className="hover:text-blue-700 hover:underline" href="/">
                  Home
                </Link>
              </li>
              <li className="flex items-center">
                <Chevron />
              </li>
              <li>
                <Link className="hover:text-blue-700 hover:underline" href="/guides/">
                  Guides
                </Link>
              </li>
              <li className="flex items-center">
                <Chevron />
              </li>
              <li>
                <span className="font-medium text-gray-800" aria-current="page">
                  Student Loans for High Earners
                </span>
              </li>
            </ol>
          </nav>

          <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mt-4 mb-4">
            Student Loans for High Earners
          </h1>
          <p className="text-xl text-gray-600">
            Essential guidance for graduates earning £50,000+ with UK student loans
          </p>
        </div>
      </section>

      {/* Intro */}
      <section className="max-w-4xl mx-auto px-4">
        <div className="prose max-w-none">
          <p>
            As a high earner with a student loan, your financial situation differs significantly from
            the average graduate. With a higher income comes accelerated repayments, different
            interest dynamics, and more complex financial decisions regarding your student debt.
          </p>
          <p>
            This guide focuses specifically on how student loans work for those earning £50,000 or
            more, helping you understand the implications for your finances and whether voluntary
            repayments might be beneficial at your income level.
          </p>
        </div>
      </section>

      {/* Repayments table */}
      <section className="max-w-4xl mx-auto px-4">
        <h2 className="text-2xl font-semibold text-gray-800 border-b border-gray-200 pb-4 mb-6">
          Student Loan Repayment Amounts for High Incomes
        </h2>

        <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100 mb-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            Monthly Repayments at Different Salary Levels
          </h3>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <Th>Salary</Th>
                  <Th>Plan 1</Th>
                  <Th>Plan 2</Th>
                  <Th>Plan 4</Th>
                  <Th>Plan 5</Th>
                  <Th>Postgrad</Th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <Tr>
                  <Td head>£50,000</Td>
                  <Td>£210</Td>
                  <Td>£170</Td>
                  <Td>£168</Td>
                  <Td>£188</Td>
                  <Td>£145</Td>
                </Tr>
                <Tr>
                  <Td head>£75,000</Td>
                  <Td>£397</Td>
                  <Td>£358</Td>
                  <Td>£356</Td>
                  <Td>£375</Td>
                  <Td>£270</Td>
                </Tr>
                <Tr>
                  <Td head>£100,000</Td>
                  <Td>£585</Td>
                  <Td>£545</Td>
                  <Td>£543</Td>
                  <Td>£563</Td>
                  <Td>£395</Td>
                </Tr>
              </tbody>
            </table>
          </div>

          <p className="text-sm text-gray-500 mt-4">
            Figures are approximate monthly repayments based on 2023/24 thresholds. Actual amounts
            may vary slightly due to tax code and exact calculation methods.
          </p>
        </div>

        <div className="prose max-w-none">
          <p>
            As the table shows, high earners make substantial monthly repayments toward their
            student loans. At £100,000, you could be repaying over £6,500 per year on a Plan 2
            loan—a significant proportion of your after-tax income.
          </p>
          <p>
            For context, a graduate on a typical starting salary of £25,000–£30,000 might repay
            less than £500 annually on a Plan 2 or Plan 5 loan, or nothing at all if they&apos;re
            below the repayment threshold.
          </p>
        </div>
      </section>

      {/* Interest rates */}
      <section className="max-w-4xl mx-auto px-4">
        <h2 className="text-2xl font-semibold text-gray-800 border-b border-gray-200 pb-4 mb-6">
          Interest Rates for High Earners
        </h2>

        <div className="prose max-w-none">
          <p>
            Your income doesn&apos;t just affect your repayment amount—for Plan 2 loans, it also
            influences the interest rate applied to your balance:
          </p>

          <h3>Plan 2 Interest Rate Structure</h3>
          <ul>
            <li>Earning up to £27,295: RPI only (currently 4.3%)</li>
            <li>Earning between £27,295 and £49,130: Interest increases gradually from RPI to RPI+3%</li>
            <li>Earning over £49,130: Maximum rate of RPI+3% (currently 7.3%)</li>
          </ul>

          <p>
            As a high earner above £49,130, you&apos;ll pay the maximum interest rate of RPI+3% on
            your Plan 2 loan. This means your loan balance grows more quickly than for lower
            earners, despite your higher repayments.
          </p>

          <h3>Other Loan Plans</h3>
          <p>For other loan plans, your interest rate is not affected by your income:</p>
          <ul>
            <li>
              <strong>Plan 1 and Plan 4:</strong> Lower of RPI or Bank Base Rate +1% (currently 1.75%)
            </li>
            <li>
              <strong>Plan 5:</strong> RPI only (currently 4.3%)
            </li>
            <li>
              <strong>Postgraduate Loan:</strong> RPI+3% (currently 7.3%)
            </li>
          </ul>

          <p>
            The key takeaway for high earners is that Plan 2 loans carry the highest interest rates,
            while Plan 1 and Plan 4 offer significantly lower rates regardless of income.
          </p>
        </div>
      </section>

      {/* Voluntary repayments */}
      <section className="max-w-4xl mx-auto px-4">
        <h2 className="text-2xl font-semibold text-gray-800 border-b border-gray-200 pb-4 mb-6">
          Should High Earners Make Voluntary Repayments?
        </h2>

        <div className="prose max-w-none">
          <p>
            As a high earner, you face a crucial decision: should you make additional voluntary
            repayments to clear your student loan faster? The answer depends on several factors:
          </p>

          <h3>When Voluntary Repayments Often Make Sense</h3>
          <ul>
            <li>
              <strong>For Plan 2 graduates earning £60,000+:</strong> You&apos;re likely to repay
              your loan in full before the 30-year write-off period, and the high interest rate
              (RPI+3%) means you&apos;ll pay more in total if you repay slowly.
            </li>
            <li>
              <strong>For Postgraduate Loan holders with high income:</strong> Similar to Plan 2,
              the high interest rate means early repayment can save substantial interest.
            </li>
            <li>
              <strong>If you&apos;re approaching full repayment:</strong> If you&apos;re within a
              few years of paying off your loan completely, accelerating repayment can reduce total
              interest.
            </li>
          </ul>

          <h3>When Voluntary Repayments Typically Don&apos;t Make Sense</h3>
          <ul>
            <li>
              <strong>For Plan 1 or Plan 4 graduates:</strong> The low interest rate (currently
              1.75%) means there&apos;s less benefit to early repayment compared to other financial
              priorities.
            </li>
            <li>
              <strong>For Plan 5 graduates earning under £80,000:</strong> Many won&apos;t repay in
              full before the 40-year write-off, so additional payments might never benefit you.
            </li>
            <li>
              <strong>If you have higher-interest debts:</strong> Credit cards or personal loans
              typically have much higher interest rates than student loans.
            </li>
            <li>
              <strong>If you haven&apos;t maximized pension contributions:</strong> The tax relief
              on pension contributions often outweighs the benefit of extra student loan repayments.
            </li>
          </ul>

          <h3>The Calculation for High Earners</h3>
          <p>
            The higher your income, the more likely you are to repay your loan in full before
            write-off. If complete repayment is inevitable, then the question becomes purely
            financial: what&apos;s the most efficient allocation of your money?
          </p>
          <p>
            For Plan 2 loans especially, with their high interest rate of RPI+3%, early repayment
            can save substantial interest costs for high earners who would otherwise repay the full
            amount via salary deductions.
          </p>
        </div>
      </section>

      {/* Tax & alternatives */}
      <section className="max-w-4xl mx-auto px-4">
        <h2 className="text-2xl font-semibold text-gray-800 border-b border-gray-200 pb-4 mb-6">
          Tax Considerations and Alternative Strategies
        </h2>

        <div className="prose max-w-none">
          <p>
            As a high earner, tax efficiency becomes increasingly important in your financial
            decisions:
          </p>

          <h3>Pension Contributions vs. Student Loan Repayment</h3>
          <p>Pension contributions reduce your taxable income and, importantly, your student loan repayment income:</p>
          <ul>
            <li>Contributing to a pension reduces the income used to calculate your 9% student loan repayment</li>
            <li>For additional rate taxpayers (£125,140+), pension contributions provide 45% tax relief</li>
            <li>Combined with student loan savings, the effective &quot;return&quot; on pension contributions can exceed 50%</li>
          </ul>

          <h3>ISAs and Investment Options</h3>
          <p>For many high earners, a balanced approach works best:</p>
          <ul>
            <li>Maximize pension contributions for tax efficiency</li>
            <li>Build emergency savings in cash ISAs</li>
            <li>Consider stocks and shares ISAs for long-term growth</li>
            <li>Make targeted student loan overpayments only when they make mathematical sense</li>
          </ul>

          <p>
            The key is to compare the effective interest rate on your student loan with the
            post-tax returns you could achieve through alternative uses of your money, while
            considering the likelihood of your loan being written off.
          </p>
        </div>
      </section>

      {/* CTA */}
      <section className="max-w-4xl mx-auto bg-gradient-to-r from-blue-500 to-blue-700 rounded-xl text-white p-8">
        <div className="text-center space-y-6">
          <h2 className="text-2xl md:text-3xl font-bold">Calculate Your High-Income Repayments</h2>
          <p className="text-lg text-blue-100">
            See exactly how much you&apos;ll repay at your salary level and whether voluntary repayments make financial sense
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/calculators/investment-vs-overpayment-calculator/"
              className="bg-white text-blue-700 font-semibold px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all"
            >
              Investment vs Overpayment Calculator
            </Link>
          </div>
        </div>
      </section>

      {/* Related resources */}
      <section className="max-w-4xl mx-auto px-4">
        <h2 className="text-2xl font-semibold text-gray-800 border-b border-gray-200 pb-4 mb-6">
          Related Resources
        </h2>

        <div className="grid md:grid-cols-2 gap-6">
          <Link
            href="/compare/student-loans-on-50k-salary/"
            className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all p-6 border border-gray-100"
          >
            <h3 className="text-xl font-semibold text-gray-800 mb-2">Student Loans on £50k Salary</h3>
            <p className="text-gray-600">
              Detailed comparison of repayments across all loan types at a £50,000 salary.
            </p>
          </Link>

          <Link
            href="/guides/investment-basics-uk/"
            className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all p-6 border border-gray-100"
          >
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              Investment Basics for UK Graduates
            </h3>
            <p className="text-gray-600">
              Learn about tax-efficient investing alternatives to student loan repayments.
            </p>
          </Link>
        </div>
      </section>
    </main>
  );
}

/** ---- tiny UI helpers ---- */
function Chevron() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="h-3 w-3 text-gray-400"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
      aria-hidden="true"
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </svg>
  );
}

function Th({ children }: { children: React.ReactNode }) {
  return <th className="px-4 py-3 text-left text-gray-700 font-semibold">{children}</th>;
}

function Tr({ children }: { children: React.ReactNode }) {
  return <tr>{children}</tr>;
}

function Td({
  children,
  head = false,
}: {
  children: React.ReactNode;
  head?: boolean;
}) {
  return (
    <td className={`px-4 py-3 ${head ? "text-gray-800 font-medium" : "text-gray-600"}`}>
      {children}
    </td>
  );
}
