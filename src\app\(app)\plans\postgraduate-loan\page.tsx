import PostgraduateLoanContent from '@/modules/plan/postgraduate-loan'
import { Metadata } from 'next';
import React from 'react'

export const metadata: Metadata = {
  title: "UK Postgraduate Student Loans Explained - Complete Guide",
  description: "Everything you need to know about UK Postgraduate Student Loans including repayment thresholds, interest rates, eligibility and how payments work for Masters and PhD students.",
  applicationName: "Student Loan Calculator UK",
  authors: [{ name: "Student Loan Calculator UK Team" }],
  generator: "Next.js",
  keywords: [
    "student loan calculator",
    "uk student loan",
    "plan 2 calculator",
    "plan 5 calculator",
    "student finance",
    "loan repayments",
  ],
  creator: "Student Loan Calculator UK",
  publisher: "Student Loan Calculator UK",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://studentloancalculator.uk/plans/postgraduate-loan/",
  },
  formatDetection: {
    telephone: false,
    address: false,
    email: false,
  },
  openGraph: {
    title: "Postgraduate Student Loans UK - Official Guide",
    description: "Complete breakdown of Postgraduate Masters and Doctoral Loans in the UK. Learn about repayment terms, thresholds, and how they differ from undergraduate loans.",
    url: "https://studentloancalculator.uk/plans/postgraduate-loan/",
    siteName: "Student Loan Calculator UK",
    locale: "en_GB",
    type: "article",
  },
  twitter: {
    card: "summary_large_image",
    title: "Student Loan Calculator UK",
    description: "Calculate your UK student loan repayments with our free calculators.",
    images: ["http://localhost:3000/og-image.jpg"],
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
    other: [{ rel: "mask-icon", url: "/safari-pinned-tab.svg" }],
  },
};

const PostGraduateLoanPage = () => {
  return (
    <div>
        <PostgraduateLoanContent/>
    </div>
  )
}

export default PostGraduateLoanPage